<template>
  <div class="chat-container">
    <div class="chat-header" style="background-color: aquamarine;">
      <h2 style="text-align: center;">小智</h2>
    </div>
    <div class="chat-body" ref="chatBody">
      <div class="message assistant">
      </div>
      <div v-for="(msg, index) in messages" :key="index">
        <template v-if="msg.role === 'assistant'">
          <div class="ai-message-row">
            <div class="ai-avatar">小智</div>
            <div class="ai-bubble">{{ msg.content.replace(/<think>|<\/think>|\*/g, '').replace(/^[\s\n]+/, '') }}</div>
          </div>
        </template>
        <template v-else>
          <div :class="['message', msg.role]">
            <div class="avatar">{{ msg.role === 'user' ? '用户' : '小智' }}</div>
            <div class="content">{{ msg.content.replace(/<think>|<\/think>/g, '') }}</div>
          </div>
        </template>
      </div>
      <!-- 显示正在思考的提示 -->
      <div v-if="isTyping" class="message assistant">
        <div class="avatar">小智</div>
        <div class="content">正在思考中...</div>
      </div>
    </div>
    <div class="chat-input">
      <textarea
          v-model="inputText"
          @keydown.enter.exact.prevent="sendMessage"
          placeholder="请输入您的医疗问题..."
      ></textarea>
      <div class="history-dropdown-wrapper">
        <button class="history-btn" @click="toggleHistoryDropdown">历史▼</button>
        <button @click="startVoice" :disabled="isRecording" class="voice-btn">
          <span v-if="!isRecording">🎤 语音</span>
          <span v-else>录音中...</span>
        </button>
        <ul v-if="showHistoryDropdown" class="history-dropdown">
          <li v-for="(item, idx) in historyList" :key="idx">
            <span @click="selectHistory(item)" class="history-item-text">{{ item }}</span>
            <span class="history-delete" @click.stop="deleteHistory(idx)">×</span>
          </li>
        </ul>
      </div>
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      messages: [
        {
          role: 'assistant',
          content: '您好！我是您的智能助手小智。我可以帮助您：\n\n1. 回答各种问题\n2. 管理数据库中的优惠券信息\n\n数据库操作示例：\n• "查询所有优惠券"\n• "查询有效优惠券"\n• "添加优惠券 春节大促"\n• "更新ID为1的优惠券名称为新春优惠"\n• "删除ID为2的优惠券"\n• "统计优惠券数量"\n\n请问有什么可以帮您的吗？'
        }
      ],
      inputText: '',
      isTyping: false,
      typingInterval: null,
      isRecording: false,
      recognition: null,
      showHistoryDropdown: false,
      historyList: []
    };
  },
  mounted() {
    this.loadHistory();
  },
  methods: {
    startVoice() {
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('当前浏览器不支持语音识别');
        return;
      }
      if (this.isRecording) return;
      this.isRecording = true;
      if (!this.recognition) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        this.recognition.lang = 'zh-CN';
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.onresult = (event) => {
          const transcript = event.results[0][0].transcript;
          this.inputText += transcript;
        };
        this.recognition.onerror = (event) => {
          alert('语音识别出错: ' + event.error);
        };
        this.recognition.onend = () => {
          this.isRecording = false;
        };
      }
      this.recognition.start();
    },
    sendMessage() {
      if (!this.inputText.trim()) return;
      const userMessage = {
        role: 'user',
        content: this.inputText
      };
      this.messages.push(userMessage);
      this.saveHistory(this.inputText);
      this.inputText = '';
      try {
        this.isTyping = true;
        this.$http.post('/api/chat', {
          messages: this.messages
        }).then(response => {
          const aiResponseLines = response.data.split('\n');
          let aiContent = '';
          let lineIndex = 0;
          this.typingInterval = setInterval(() => {
            if (lineIndex < aiResponseLines.length) {
              if (aiContent) {
                aiContent += '\n';
              }
              aiContent += aiResponseLines[lineIndex];
              this.scrollToBottom();
              lineIndex++;
            } else {
              clearInterval(this.typingInterval);
              this.isTyping = false;
              this.messages.push({
                role: 'assistant',
                content: aiContent
              });
              this.scrollToBottom();
            }
          }, 500);
        }).catch(error => {
          console.error('Error:', error);
          this.messages.push({
            role: 'assistant',
            content: '抱歉，系统出现错误，请稍后再试。'
          });
          this.isTyping = false;
        });
      } catch (error) {
        console.error('Error:', error);
        this.messages.push({
          role: 'assistant',
          content: '抱歉，系统出现错误，请稍后再试。'
        });
        this.isTyping = false;
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.$refs.chatBody.scrollTop = this.$refs.chatBody.scrollHeight;
      });
    },
    // 历史记录相关
    saveHistory(text) {
      if (!text) return;
      let history = JSON.parse(localStorage.getItem('chat_history') || '[]');
      if (!history.includes(text)) {
        history.unshift(text);
        if (history.length > 20) history = history.slice(0, 20);
        localStorage.setItem('chat_history', JSON.stringify(history));
      }
      this.loadHistory();
    },
    loadHistory() {
      this.historyList = JSON.parse(localStorage.getItem('chat_history') || '[]');
    },
    toggleHistoryDropdown() {
      this.showHistoryDropdown = !this.showHistoryDropdown;
    },
    selectHistory(item) {
      this.inputText = item;
      this.showHistoryDropdown = false;
    },
    deleteHistory(idx) {
      this.historyList.splice(idx, 1);
      localStorage.setItem('chat_history', JSON.stringify(this.historyList));
    }
  }
};
</script>

<style scoped>
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

.chat-header {
  flex-shrink: 0;
}

.chat-body {
  flex: 1;
  height: auto;
  min-height: 0;
  overflow-y: auto;
  padding-bottom: 20px;
}

.chat-input {
  flex-shrink: 0;
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
  align-items: center;
}

textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  resize: none;
  margin-right: 10px;
  font-size: 16px;
  transition: border 0.3s;
}

textarea:focus {
  outline: none;
  border-color: #4CAF50;
}

button {
  padding: 12px 25px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #3d8b40;
}

.message {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;  /* 顶部对齐 */
}

.user {
  justify-content: flex-end;
}

.assistant {
  justify-content: flex-start;
}

.avatar {
  font-size: 20px;
  margin-right: 10px;
  color: #4CAF50;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
  line-height: 1.2;
  background-color: #f0f0f0;
  border-radius: 50%;
  padding: 6px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  align-self: flex-start;
}

.content {
  max-width: 60%;
  min-width: 80px;
  display: inline-block;
  word-break: break-all;
  white-space: pre-line;
  padding: 10px 15px 10px 15px;
  border-radius: 10px;
  line-height: 1.5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-top: 0;
  font-size: 15px;
}

.user .content {
  background-color: #e8f4ff;
  order: -1; /* 用户消息头像在右侧 */
}

.assistant .content {
  background-color: #f0f8ff;
  color: #2d3a4b;
  font-size: 16px;
  line-height: 1.8;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  padding: 0 18px 12px 18px; /* 修改顶部padding为0 */
  margin-top: 0;
}

.ai-message-row {
  display: flex;
  align-items: flex-start;
  margin: 0 !important;
  padding: 0 !important;
}

.ai-avatar {
  width: 32px;
  height: 32px;
  background: #e0f7fa;
  color: #26a69a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin: 0 8px 0 0 !important;
  flex-shrink: 0;
  line-height: 32px;
  padding: 0 !important;
}

.ai-bubble {
  background: #f0f8ff;
  color: #234;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  font-size: 17px;
  line-height: 1.5 !important;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  padding: 0 22px 12px 22px !important;
  margin: 0 !important;
  max-width: 60vw;
  min-width: 40px;
  word-break: break-all;
  white-space: pre-line;
  letter-spacing: 0.5px;
  align-self: flex-start;
  display: block;
}

.message,
.assistant {
  margin: 0 !important;
  padding: 0 !important;
}

.voice-btn {
  margin-right: 10px;
  padding: 12px 16px;
  background: #fff;
  border: 1px solid #4CAF50;
  color: #4CAF50;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s, color 0.3s;
}

.voice-btn:disabled {
  color: #aaa;
  border-color: #aaa;
  cursor: not-allowed;
}

.history-dropdown-wrapper {
  position: relative;
  margin-left: 0;
  display: flex;
  align-items: center;
}

.history-btn,
.voice-btn {
  padding: 12px 25px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-left: 10px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}
.history-btn:hover,
.voice-btn:hover {
  background-color: #3d8b40;
}

.history-dropdown {
  position: absolute;
  bottom: 110%;
  left: 0;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.08);
  min-width: 120px;
  max-height: 220px;
  overflow-y: auto;
  z-index: 10;
  padding: 0;
  margin: 0;
  list-style: none;
}

.history-dropdown li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-item-text {
  flex: 1;
  cursor: pointer;
}

.history-delete {
  color: #aaa;
  margin-left: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: color 0.2s;
}

.history-delete:hover {
  color: #f44336;
}
</style>